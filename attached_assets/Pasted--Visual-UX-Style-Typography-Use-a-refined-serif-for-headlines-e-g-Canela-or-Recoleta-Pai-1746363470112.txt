🎨 Visual & UX Style
Typography:

Use a refined serif for headlines (e.g., Canela or Recoleta).

Pair with a clean neo-grotesque sans-serif for body text (e.g., <PERSON><PERSON><PERSON><PERSON>, Neue Haas Grotesk, or Inter).

Maintain high contrast and generous letter spacing.

Grid & Layout:

Wide, generous layout with white space that feels editorial.

Use a modular grid system (CSS Grid or Tailwind CSS grid) to create asymmetry and structure.

Fluid, responsive layout — must feel natural across breakpoints.

Color Scheme:

Muted neutrals (off-whites, greys, blacks) with subtle overlays.

Use soft gradients, subtle texture, and layering for depth.

UI Elements:

Smooth, unobtrusive transitions.

Hover effects with slight opacity or displacement (GSAP or Framer Motion).

Clean hamburger nav for mobile with full-screen overlay.

Scroll-triggered section reveals (see ScrollTrigger or Framer Motion).

🧠 UX Interactions
Navigation:

Fixed header with dynamic color change based on scroll or section background.

Section-to-section navigation with smooth scroll and anchor snapping.

Animations:

Use Framer Motion or GSAP for:

Hero image scaling on scroll.

Text entrance animation (fade, slide).

Button hover states.

Parallax or scroll effects for visual engagement.

Microinteractions:

Cursor-follow or hover highlights on interactive content.

Use Lottie or lightweight SVG animations sparingly.

⚙️ React Implementation Guide
Tech Stack:

React + Vite/Next.js (for performance).

Tailwind CSS for utility-first styling (custom theme setup).

Framer Motion for transitions and scroll effects.

Optional: Sanity.io or Headless CMS for content control.

Component Breakdown:

Header: sticky, animated on scroll

HeroSection: full-screen, animated intro text + background

AboutSection: grid layout with text/image

WorkGrid: responsive image grid with hover interactions

Footer: simple with social links and contact

Performance:

Lazy-load images and video.

Use react-intersection-observer for scroll effects.

Use next/image or ImageOptim (if using Vite) for optimized assets.

📐 Deliverables
Design system (Figma or Sketch) matching Reflektor’s tone.

Responsive prototype with interaction annotations.

React component library built modularly, matching designs 1:1.

Staging link with scroll-based transitions and animations.

