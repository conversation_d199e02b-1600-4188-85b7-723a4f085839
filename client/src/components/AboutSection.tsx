import { motion } from "framer-motion";
import useScrollAnimation from "@/hooks/useScrollAnimation";

interface TeamMember {
  id: string;
  name: string;
  role: string;
  image: string;
}

const teamMembers: TeamMember[] = [
  {
    id: "team1",
    name: "McDonald's",
    role: "Website Rebuild | Drupal CMS",
    image: "/assets/McD_logo.webp",
  },
  {
    id: "team2",
    name: "Sainsbury's",
    role: "Email Marketing | Data Processing | Security Compliance",
    image: "/assets/JS_logo.webp",
  },
  {
    id: "team3",
    name: "National Health Service",
    role: "Infrastructure & Software Management",
    image: "/assets/NHS_logo.webp",
  },
  {
    id: "team4",
    name: "Starwood Hotels",
    role: "Website Rebuild",
    image: "/assets/SW_logo.webp",
  },
];

const AboutSection = () => {
  const titleRef = useScrollAnimation();
  const text1Ref = useScrollAnimation();
  const text2Ref = useScrollAnimation();
  const text3Ref = useScrollAnimation();
  const servicesRef = useScrollAnimation();
  const imageRef = useScrollAnimation();
  const approachRef = useScrollAnimation();
  const teamTitleRef = useScrollAnimation();

  return (
    <section id="about" className="pt-32 pb-20 px-6 md:px-12 xl:px-24 bg-rich-black text-off-white">
      <div className="max-w-[1920px] mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <div>
            <h2 
              ref={titleRef}
              className="font-serif text-4xl md:text-5xl font-medium tracking-heading mb-12 fade-in"
            >
              About Me
            </h2>
            <div className="space-y-6">
              <p 
                ref={text1Ref}
                className="text-lg tracking-wider fade-in"
              >
                I am a results-driven Project Manager with a strong track record leading complex digital, IT, and transformation projects. Adept at managing cross-functional teams and delivering solutions in fast-paced environments. Experienced in stakeholder management, resource allocation, and budget control, ensuring projects meet strategic goals efficiently.
              </p>
              <p 
                ref={text2Ref}
                className="text-lg tracking-wider fade-in"
              >
                Passionate about leveraging technology to drive innovation and operational excellence. Proficient in Agile and Prince 2 methodologies, with hands-on expertise in IT infrastructure, CRM, marketing technology, and data-driven solutions.
              </p>
              <p 
                ref={text3Ref}
                className="text-lg tracking-wider fade-in"
              >
                With a diverse array of experience and knowledge rooted in solving technical problems. Starting out as a reprographics art-worker and graphic designer in the print industry, I moved to coding and software development. I am therefore very comfortable working with multi-disciplinary design and software teams, very often in creative agency environments.
                </p>
                <p 
                  ref={text3Ref}
                  className="text-lg tracking-wider fade-in"
                >
                  As an early adopter of content management system design, focusing on Drupal & eCommerce, developing some of the earliest Headless CMS, using custom APIs, integrating legacy backend content systems. I'm now enjoying the advances in AI and LLM solutions. <br/>
                  Privileged to have produced over 200 web, eCommerce and software development projects, and work with some really great people in the industry.
                </p>
                <p 
                  ref={text3Ref}
                  className="text-lg tracking-wider fade-in"
                >
                Pivoting towards emerging technologies, I am currently providing AI consulting and automation services. This involves collaborating with select companies to scope and develop MVPs/POCs that may apply AI solutions, agents, automation & LLM architecture for significant efficiency gains or to validate novel business concepts.
              </p>
              <p 
                ref={text3Ref}
                className="text-lg tracking-wider fade-in"
              >
                Fractional Security and AI risk management, compliance & implementation, including ISO/IEC 27001:2022 & ISO/IEC 42001:2023 Artificial intelligence — Management system.
              </p>
            </div>

            <div 
              ref={servicesRef}
              className="mt-12 fade-in"
            >
              <h3 className="font-serif text-2xl tracking-heading mb-8">Skill Set</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 w-full">
                <div>
                  <p className="tracking-wider mb-2">Project & Change Management</p>
                  <p className="tracking-wider mb-2">IT Infrastructure & Software Deployment</p>
                  <p className="tracking-wider mb-2">Email Design & Build Workflows</p>
                  <p className="tracking-wider mb-2">Marketing Automation (HubSpot, Marketo, Salesforce)</p>
                  <p className="tracking-wider mb-2">Large scale Web Development (CMS + Drupal)</p>
                  <p className="tracking-wider mb-2">Technical Search Engine Optimisation SEO</p>
                </div>
                <div>
                  <p className="tracking-wider mb-2">ISO 27001, 9001, 42001 Implementation & Certification</p>
                  <p className="tracking-wider mb-2">GDPR and Data Protection</p>
                  <p className="tracking-wider mb-2">Data Security & Compliance</p>
                  <p className="tracking-wider mb-2">Ai Agents, Automation Workflow</p>
                  <p className="tracking-wider mb-2">Ai Risk Management & Compliance</p>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div 
              ref={imageRef}
              className="h-full w-full fade-in"
            >
              <video
                src="/assets/altaire-office.mp4"
                className="w-full h-full object-cover"
                autoPlay
                muted
                playsInline
              />
            </div>

            <motion.div
              ref={approachRef}
              className="absolute -bottom-12 -right-12 lg:-left-12 z-10 bg-off-white text-rich-black p-8 max-w-md fade-in hidden md:block"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h3 className="font-serif text-xl tracking-heading mb-4">My Approach</h3>
              <p className="text-sm tracking-wider">
                I believe in the power of collaborative teams. Combining strategic thinking, creative exploration, and technical expertise to deliver meaningful digital experiences.
              </p>
            </motion.div>
          </div>
        </div>

        <div className="mt-32">
          <h3 
            ref={teamTitleRef}
            className="font-serif text-2xl tracking-heading mb-12 fade-in"
          >
            Some Brands I've Worked With
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <TeamMember key={member.id} member={member} index={index} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

interface TeamMemberProps {
  member: TeamMember;
  index: number;
}

const TeamMember = ({ member, index }: TeamMemberProps) => {
  const memberRef = useScrollAnimation();

  return (
    <motion.div
      ref={memberRef}
      className="fade-in"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
    >
      <div className="aspect-square overflow-hidden mb-4">
        <img
          src={member.image}
          alt={member.name}
          className="object-contain w-7/8 h-7/8 mx-auto grayscale hover:grayscale-0 transition-all duration-500"
          loading="lazy"
        />
      </div>
      <h4 className="font-medium tracking-wider mb-1">{member.name}</h4>
      <p className="text-sm text-medium-gray tracking-wider">{member.role}</p>
    </motion.div>
  );
};

export default AboutSection;
