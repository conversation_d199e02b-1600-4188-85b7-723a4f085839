import { useState } from "react";
import { motion } from "framer-motion";
import useScrollAnimation from "@/hooks/useScrollAnimation";

interface FormData {
  name: string;
  email: string;
  message: string;
}

const ContactSection = () => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    message: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // You would normally send the form data to a server here
    console.log("Form submitted:", formData);
    // Reset form after submission
    setFormData({ name: "", email: "", message: "" });
  };

  const titleRef = useScrollAnimation();
  const subtitleRef = useScrollAnimation();
  const formRef = useScrollAnimation();
  const contactInfoRef = useScrollAnimation();

  return (
    <section id="contact" className="pt-32 pb-20 px-6 md:px-12 xl:px-24">
      <div className="max-w-[1920px] mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <div>
            <h2
              ref={titleRef}
              className="font-serif text-4xl md:text-5xl font-medium tracking-heading mb-8 fade-in"
            >
              Let's create something meaningful together
            </h2>
            <p
              ref={subtitleRef}
              className="text-lg text-medium-gray tracking-wider max-w-xl mb-12 fade-in"
            >
              I am looking for new challenges and collaborations. Get in touch to discuss your project.
            </p>

            <form
              className="space-y-8 max-w-xl fade-in"
              ref={formRef}
              onSubmit={handleSubmit}
            >
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm tracking-wider mb-2"
                >
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full border-b border-light-gray pb-2 bg-transparent focus:border-rich-black outline-none transition-colors"
                  placeholder="Your name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-sm tracking-wider mb-2"
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full border-b border-light-gray pb-2 bg-transparent focus:border-rich-black outline-none transition-colors"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="message"
                  className="block text-sm tracking-wider mb-2"
                >
                  Message
                </label>
                <textarea
                  id="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full border-b border-light-gray pb-2 bg-transparent focus:border-rich-black outline-none transition-colors"
                  placeholder="Tell me about your project and how I can help..."
                  required
                ></textarea>
              </div>

              <motion.button
                type="submit"
                className="bg-rich-black text-off-white py-3 px-8 inline-flex items-center group hover-scale"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <span className="tracking-wider">Send message</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                  />
                </svg>
              </motion.button>
            </form>
          </div>

          <div className="lg:pl-12 fade-in" ref={contactInfoRef}>
            <h3 className="font-serif text-2xl tracking-heading mb-8">
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-12">
              <div>
                <h4 className="text-sm font-medium tracking-wider mb-4">
                  Martin Henwood
                </h4>
                <p className="text-medium-gray tracking-wider mb-1">
                  Dorset
                </p>
                <p className="text-medium-gray tracking-wider mb-1">
                  United Kingdom
                </p>
                <p className="text-medium-gray tracking-wider mt-4">
                  +44 7511552862
                </p>
                <p className="text-medium-gray tracking-wider">
                  <EMAIL>
                </p>
              </div>

              

              <div className="md:col-span-2">
                <h4 className="text-sm font-medium tracking-wider mb-4">
                  Follow Me
                </h4>
                <div className="flex space-x-6">
                  <a
                    href="https://www.instagram.com/martinhenwood/"
                    className="text-medium-gray hover:text-rich-black transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Instagram
                  </a>
                  <a
                    href="https://x.com/MartinHenwood"
                    className="text-medium-gray hover:text-rich-black transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Twitter
                  </a>
                  <a
                    href="https://www.linkedin.com/in/martinhenwood/"
                    className="text-medium-gray hover:text-rich-black transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    LinkedIn
                  </a>
                  <a
                    href="#"
                    className="text-medium-gray hover:text-rich-black transition-colors"
                    onClick={(e) => {
                      e.preventDefault();
                      window.scrollTo({ top: 0, behavior: 'smooth' });
                    }}
                  >
                    Home
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
