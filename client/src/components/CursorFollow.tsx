import { useEffect, useState } from "react";
import { motion } from "framer-motion";

const CursorFollow = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVariant, setCursorVariant] = useState("default");
  const [isMobile, setIsMobile] = useState(false);

  const variants = {
    default: {
      x: mousePosition.x - 25,
      y: mousePosition.y - 25,
      width: 75,
      height: 75,
      backgroundColor: "rgba(0, 0, 0, 0.1)",
    },
    hover: {
      x: mousePosition.x - 40,
      y: mousePosition.y - 40,
      width: 120,
      height: 120,
      backgroundColor: "rgba(0, 0, 0, 0.05)",
    },
  };

  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 1024);
    };
    
    // Initial check
    checkMobile();
    
    // Listen for resize events
    window.addEventListener("resize", checkMobile);

    // Only add mouse event listeners if not on mobile
    if (!isMobile) {
      const mouseMove = (e: MouseEvent) => {
        setMousePosition({ x: e.clientX, y: e.clientY });
      };

      window.addEventListener("mousemove", mouseMove);

      // Add event listeners for interactive elements
      const interactiveElements = document.querySelectorAll(
        "a, button, .project-item"
      );

      interactiveElements.forEach((el) => {
        el.addEventListener("mouseenter", () => setCursorVariant("hover"));
        el.addEventListener("mouseleave", () => setCursorVariant("default"));
      });

      // Cleanup function
      return () => {
        window.removeEventListener("mousemove", mouseMove);
        window.removeEventListener("resize", checkMobile);
        
        interactiveElements.forEach((el) => {
          el.removeEventListener("mouseenter", () => setCursorVariant("hover"));
          el.removeEventListener("mouseleave", () => setCursorVariant("default"));
        });
      };
    }
    
    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, [isMobile]);

  // Don't render on mobile
  if (isMobile) return null;

  return (
    <motion.div
      className="cursor-follow fixed pointer-events-none z-[9999] rounded-full mix-blend-difference"
      variants={variants}
      animate={cursorVariant}
      transition={{ type: "spring", stiffness: 500, damping: 28 }}
    />
  );
};

export default CursorFollow;
