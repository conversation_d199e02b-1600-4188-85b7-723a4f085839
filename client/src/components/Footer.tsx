import { motion } from "framer-motion";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <motion.footer 
      className="py-8 px-6 md:px-12 xl:px-24 border-t border-light-gray"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <div className="max-w-[1920px] mx-auto flex flex-col md:flex-row justify-between items-center">
        <div className="mb-4 md:mb-0">
          <p className="text-sm text-medium-gray tracking-wider">
            © {currentYear} Quaark - A Digital Edge. All rights reserved.
          </p>
        </div>

        <div>
          <ul className="flex space-x-8">
            <li>
              <a
                href="#"
                className="text-sm text-medium-gray tracking-wider hover:text-rich-black transition-colors"
              >
                Privacy
              </a>
            </li>
            <li>
              <a
                href="#"
                className="text-sm text-medium-gray tracking-wider hover:text-rich-black transition-colors"
              >
                Terms
              </a>
            </li>
            <li>
              <a
                href="#"
                className="text-sm text-medium-gray tracking-wider hover:text-rich-black transition-colors"
              >
                Cookies
              </a>
            </li>
          </ul>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
