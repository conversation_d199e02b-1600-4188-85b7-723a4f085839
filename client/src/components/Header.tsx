import { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface HeaderProps {
  toggleMenu: () => void;
  isMenuOpen: boolean;
}

const Header = ({ toggleMenu, isMenuOpen }: HeaderProps) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <motion.header
      className={`fixed top-0 left-0 w-full z-50 py-6 px-6 md:px-12 transition-colors duration-400 ${
        isScrolled ? "bg-off-white shadow-sm" : "bg-transparent"
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeInOut" }}
      data-transparent={!isScrolled}
    >
      <div className="max-w-[1920px] mx-auto flex justify-between items-center">
        <a href="#" className="z-50 relative flex flex-col items-start">
          <img 
            src="/assets/quaark-logo.png" 
            alt="Quaark" 
            className="h-15"
          />
          <span className="text-sm tracking-wide mt-1 text-center">Project Management | Digital Transformation | Systems Integration | AI</span>
        </a>

        <div className="hidden md:flex space-x-8 items-center">
          <a
            href="#work"
            className="text-sm tracking-wider hover:opacity-70 transition-opacity"
          >
            Work
          </a>
          <a
            href="#about"
            className="text-sm tracking-wider hover:opacity-70 transition-opacity"
          >
            About
          </a>
          <a
            href="#contact"
            className="text-sm tracking-wider hover:opacity-70 transition-opacity"
          >
            Contact
          </a>
        </div>

        <button
          id="menu-toggle"
          className="z-50 flex flex-col justify-between w-8 h-6 md:hidden"
          onClick={toggleMenu}
          aria-label="Toggle menu"
          aria-expanded={isMenuOpen}
        >
          <span
            className={`w-full h-[1.5px] bg-current transition-all duration-300 ${
              isMenuOpen ? "transform translate-y-[7px] rotate-45" : ""
            }`}
          ></span>
          <span
            className={`w-full h-[1.5px] bg-current transition-all duration-300 ${
              isMenuOpen ? "opacity-0" : ""
            }`}
          ></span>
          <span
            className={`w-full h-[1.5px] bg-current transition-all duration-300 ${
              isMenuOpen ? "transform -translate-y-[7px] -rotate-45" : ""
            }`}
          ></span>
        </button>
      </div>
    </motion.header>
  );
};

export default Header;
