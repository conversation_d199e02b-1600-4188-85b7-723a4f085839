import { motion } from "framer-motion";
import useScrollAnimation from "@/hooks/useScrollAnimation";

const HeroSection = () => {
  const titleRef = useScrollAnimation();
  const subtitleRef = useScrollAnimation();
  const locationRef = useScrollAnimation();

  return (
    <section
      id="hero"
      className="h-screen flex flex-col justify-center relative overflow-hidden"
    >
      <div className="absolute inset-0 -z-10">
        <img
          src="/assets/Quaark-Background_Grey.jpg"
          alt="Digital creativity"
          className="object-cover h-full w-full opacity-20 slow-zoom"
          loading="eager"
        />
      </div>

      <div className="container mx-auto px-6 md:px-12 xl:px-24 pt-24">
        <div className="max-w-4xl">
          <motion.h2
            ref={titleRef}
            className="font-serif text-4xl md:text-6xl lg:text-7xl font-medium leading-tight tracking-heading mb-6 fade-in"
            initial={{ opacity: 0, y: 45 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Past. Present. Prototype.<br/>My Digital Journey
          </motion.h2>
          <motion.p
            ref={subtitleRef}
            className="text-medium-gray text-xl md:text-2xl tracking-wider max-w-xl fade-in"
            initial={{ opacity: 0, y: 45 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            A story of code, creativity, and curiosity.
          </motion.p>
        </div>

        <div className="absolute bottom-16 left-0 w-full px-6 md:px-12 xl:px-24">
          <div className="flex justify-between items-end">
            <motion.div
              ref={locationRef}
              className="fade-in"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <p className="text-medium-gray tracking-wider">
                Based in Bournemouth, Dorset UK
              </p>
            </motion.div>
            <motion.a
              href="#work"
              className="group flex items-center space-x-2 fade-in"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <span className="tracking-wider">A Digital Edge</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-5 h-5 transform group-hover:translate-x-1 transition-transform"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                />
              </svg>
            </motion.a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;