import { motion } from "framer-motion";

interface MobileNavProps {
  isOpen: boolean;
  closeMenu: () => void;
}

const MobileNav = ({ isOpen, closeMenu }: MobileNavProps) => {
  const navVariants = {
    closed: {
      clipPath: "circle(0% at calc(100% - 45px) 45px)",
      transition: {
        duration: 0.6,
        ease: [0.77, 0, 0.175, 1],
      },
    },
    open: {
      clipPath: "circle(150% at calc(100% - 45px) 45px)",
      transition: {
        duration: 0.6,
        ease: [0.77, 0, 0.175, 1],
      },
    },
  };

  const linkVariants = {
    closed: {
      opacity: 0,
      y: 20,
    },
    open: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.3 + i * 0.1,
        duration: 0.4,
      },
    }),
  };

  return (
    <motion.div
      id="nav-overlay"
      className="fixed inset-0 z-40 bg-off-white flex flex-col items-center justify-center"
      initial="closed"
      animate={isOpen ? "open" : "closed"}
      variants={navVariants}
    >
      <nav className="h-full flex flex-col justify-center items-center text-center space-y-10">
        <motion.a
          href="#work"
          className="font-serif text-5xl tracking-heading hover:opacity-70 transition-opacity"
          custom={0}
          variants={linkVariants}
          onClick={closeMenu}
        >
          Work
        </motion.a>
        <motion.a
          href="#about"
          className="font-serif text-5xl tracking-heading hover:opacity-70 transition-opacity"
          custom={1}
          variants={linkVariants}
          onClick={closeMenu}
        >
          About
        </motion.a>
        <motion.a
          href="#contact"
          className="font-serif text-5xl tracking-heading hover:opacity-70 transition-opacity"
          custom={2}
          variants={linkVariants}
          onClick={closeMenu}
        >
          Contact
        </motion.a>
      </nav>
    </motion.div>
  );
};

export default MobileNav;
