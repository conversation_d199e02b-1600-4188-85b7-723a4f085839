import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";

interface Project {
  id: string;
  image: string;
  title: string;
  category: string;
  description?: string;
}

interface ProjectModalProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
}

const ProjectModal: React.FC<ProjectModalProps> = ({
  project,
  isOpen,
  onClose,
}) => {
  // If no project or modal is closed, don't render anything
  if (!project) return null;

  // Get project data
  const { id, description } = project;

  // Create custom modal content based on project ID
  const getProjectContent = () => {
    switch (id) {
      case "project1":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">
                AREAS OF FOCUS
              </h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Currently leading multiple IT and digital transformation
                  projects for NHS trusts across the South of England, adhering
                  to Prince 2 methodology and ensuring compliance with NHS
                  requirements and standards.
                </li>
                <li>
                  Working with cross-functional teams to deliver various
                  technical initiatives, including upgrading Data Centre
                  enterprise server hardware and VM migrations.
                </li>
                <li>
                  PC and laptop replacements across 100's of GP practice sites.
                </li>
                <li>
                  The project management of GP practice site moves to new
                  locations.
                </li>
                <li>
                  Implementing software and data solutions. Delivering model,
                  pilot and POC testing, final phased deployment and a BAU
                  service transition.
                </li>
              </ul>
            </div>
          </>
        );

      case "project2":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">
                AREAS OF FOCUS
              </h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Worked across the fast-paced Sainsbury's account with the
                  Ogilvy Experience team, predominately focused on email BAU and
                  CRM campaigns plus print direct mail design and production.
                </li>
                <li>
                  Led a 10-person creative team delivering 130+ email and print
                  campaigns with 100% on-time completion.
                </li>
                <li>
                  Daily planning, project scoping, resource management, daily
                  briefings and work in progress meetings, estimates, budgeting
                  and finance.
                </li>
                <li>
                  Manage final output via HOGARTH and their talented production
                  team, producing fast turnaround of digital & print ready
                  artwork.
                </li>
              </ul>
            </div>
          </>
        );

      case "project3":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed mb-6">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">AREAS OF FOCUS</h3>
              <p className="text-base leading-relaxed mb-4">
                Maintained several responsibilities during nearly 8 years for ALTAIRE, including:
              </p>
              <p className="text-base leading-relaxed mb-2">
                Project management of the design and software development team, including;
                Data Dashboards, CRM data-driven marketing, proprietary marketing software and data-driven VR
                solutions.
              </p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li className="text-base leading-relaxed">
                  Managed the dynamic workflow of one of the best email production teams in the UK.
                </li>
                <li className="text-base leading-relaxed">
                  Finance tracking/invoicing of 3-400 projects per year.
                </li>
                <li className="text-base leading-relaxed">
                  IT maintenance & technical support for company computers to keep within security compliance.
                </li>
                <li className="text-base leading-relaxed">
                  Implementation of several ISO frameworks from the ground up, including restructuring the company to
                  align with processes, writing new policies and procedures covering;
                </li>
              </ul>
              <ul className="list-disc pl-10 space-y-1 mb-4">
                <li className="text-base leading-relaxed">
                  ISO 27001 Information Security Management
                </li>
                <li className="text-base leading-relaxed">
                  ISO 27017 Information Technology – Security controls for cloud services
                </li>
                <li className="text-base leading-relaxed">
                  ISO 27018 Information Technology – Protection of PII and cloud based PII processes
                </li>
                <li className="text-base leading-relaxed">
                  ISO 9001 Quality Management
                </li>
                <li className="text-base leading-relaxed">
                  IASME Governance – Cyber essentials and GDPR
                </li>
              </ul>
              <p className="text-base leading-relaxed mb-4">
                Certification as a lead auditor for 27k, 9k, 14k, 18k, auditing suppliers, data centres and conducting annual
                external certification.
              </p>
              <div className="mt-4 bg-gray-100 p-4 rounded-sm">
                <h4 className="font-medium mb-2">TECHNOLOGIES</h4>
                <p className="text-base">HTML5/CSS3/JavaScript/LAMP & Ruby Frameworks/AWS</p>
              </div>
            </div>
          </>
        );

      case "project4":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">
                AREAS OF FOCUS
              </h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Program owner of Martin & Co's property, estate and lettings agent website including 180 microsites.
                </li>
                <li>
                  Project management of the in-house digital and software dev. team at their head office in Bournemouth.
                </li>
                <li>
                  Achieved 90%+ first-page rankings for target industry keywords, driving 63% increase in traffic.
                </li>
                <li>
                  Lead the technical and content architecture, including correct coding to ensure successful SEO results.
                </li>
                <li>
                  Increased mobile engagement by developing mobile and tablet site builds.
                </li>
                <li>
                  Backend integration with Rightmove and Zoopla for image and customer API feeds.
                </li>
                <li>
                  Hubspot CRM workflow and trigger automation.
                </li>
                <li>
                  10x increase in customer database acquisition using HubSpot integration.
                </li>
                <li>
                  Grew the company’s content strategies, social media and newsletter channels.
                </li>
                <li>
                  Stakeholder management and reporting to internal departments and franchise owners.
                </li>
              </ul>
              <div className="mt-4 bg-gray-100 p-4 rounded-sm">
                <h4 className="font-medium mb-2">TECHNOLOGIES</h4>
                <p className="text-base">JS/JQuery/Symfony/Solr/Custom APIs/LAMP/Hubspot/SEO Tools</p>
              </div>
            </div>
          </>
        );

      case "project5":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">
                AREAS OF FOCUS
              </h3>
              <p className="text-base leading-relaxed mb-4">
                Appointed with the role ‘Head of digital’ and the general project management of operations and staff within 2 offices, Singapore and Manila. Managing the resources of sales teams, distribution, editorial, design, programming, HR and accounting.
              </p>
              <p className="text-base leading-relaxed mb-2">
                Sourcing and implementing of technical infrastructure including CRM, website, email marketing, iPad app, accounting and invoicing processes. Oversaw the production of the printed magazine, eNewsletter, eDM’s and developing new commercial communication channels.
              </p>
              <p className="text-base leading-relaxed mb-2">
                -Accomplished grant funding of s$150k from MDA (Singapore’s Gov. Media Development Authority), with a written marketing and commercial plan.
              </p>
              <div className="mt-4 bg-gray-100 p-4 rounded-sm">
                <h4 className="font-medium mb-2">Platforms & key points</h4>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Salesforce customisation.
                </li>
                <li>
                  eMail comms strategy for sales and lead acquisition, plus build consumer database.
                </li>
                <li>
                  Execution though Marketo and Salesforce integration.
                </li>
                <li>
                  Design + build Propertylife.asia first and second phase websites, Drupal CMS architecture.
                </li>
                <li>
                  Design and produce the Magazines iPad versions.
                </li>
                <li>
                  Digital planning for content delivery, social media and data management.
                </li>
              </ul>
                <div className="mt-4 bg-gray-100 p-4 rounded-sm">
                  <h4 className="font-medium mb-2">TECHNOLOGIES</h4>
                  <p className="text-base">Salesforce/Marketo/Drupal CMS/Adobe/Apple iOS/LAMP Stack/API</p>
                </div>
                </div>
            </div>
          </>
        );

      case "project6":
        return (
          <>
            <p className="text-base md:text-lg leading-relaxed">
              {description}
            </p>
            <div className="mt-6">
              <h3 className="font-serif text-xl font-medium mb-3">
                AREAS OF FOCUS
              </h3>
              <p className="text-base leading-relaxed mb-4">
                Developed a complete mobile integrated solution for lifecycle marketing using the Pointpal mobile application. Pointpal uses QR codes to deliver an effective loyalty program for retailers offering ‘buy X get Y free’ promotions.
              </p>
              <p className="text-base leading-relaxed mb-2">
                Create a digital strategy for the Singapore region. Building a technical framework to deliver communication and reporting for both consumers and merchant retailers.
              </p>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Integration of Salesforce and ExactTarget through API.
                </li>
                <li>
                  Db schema designed for ExactTarget consumer communication.
                </li>
                <li>
                  Implement Triggers and Event based comms (Email).
                </li>
                <li>
                  Design reports and KPI dashboards for marketing intelligence and billing (Excel).
                </li>
                <li>
                  Build reports from Data pulled from Salesforce + ExactTarget.
                </li>
                <li>
                  Create a mobile app for the Pointpal QR code scanner.
                </li>
                <li>
                  Design ‘Meta Data’ and categorisation strategy for Merchant Offers.
                </li>
                <li>
                  Digital planning for content delivery, social media and data management.
                </li>
              </ul>
              <div className="mt-4 bg-gray-100 p-4 rounded-sm">
                <h4 className="font-medium mb-2">TECHNOLOGIES</h4>
                <p className="text-base">Salesforce/ExactTarget/AWS/Apple iOS/HTML/JS/CSS/API</p>
              </div>
            </div>
          </>
        );

      default:
        // Default content if no specific content is defined
        return (
          <p className="text-base md:text-lg leading-relaxed">{description}</p>
        );
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/80 z-50 overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 md:p-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-off-white w-full max-w-4xl max-h-[90vh] overflow-auto rounded-sm"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 50, opacity: 0 }}
              transition={{ duration: 0.3 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 z-10 bg-black text-white p-2 rounded-full hover:bg-gray-800 transition-colors"
                aria-label="Close modal"
              >
                <X size={20} />
              </button>

              {/* Project image */}
              <div className="w-full aspect-video relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Project details */}
              <div className="p-6 md:p-8">
                <h2 className="font-serif text-3xl md:text-4xl tracking-heading mb-2 text-rich-black">
                  {project.title}
                </h2>
                <p className="text-medium-gray text-sm mb-6 tracking-wider">
                  {project.category}
                </p>
                <div className="prose max-w-none text-medium-gray">
                  {getProjectContent()}
                </div>
                <div className="mt-8">
                  <button
                    onClick={onClose}
                    className="inline-flex items-center text-rich-black hover:text-medium-gray transition-colors group"
                  >
                    <span className="tracking-wider">Back to projects</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ProjectModal;
