import { useState } from "react";
import { motion } from "framer-motion";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import ProjectModal from "./ProjectModal";
import { useIsMobile } from "@/hooks/use-mobile";

interface Project {
  id: string;
  image: string;
  title: string;
  category: string;
  description?: string;
}

const projects: Project[] = [
  {
    id: "project1",
    image: "/assets/TT-Door.jpg",
    title: "Trusted Technology Partnership",
    category: "TECHNICAL PROJECT MANAGER | April 2023 – Present",
    description: "Trusted Technology specialise in providing technology services in the healthcare sector, including complete IT and software life-cycle management,  IT service support and asset management."
  },
  {
    id: "project2",
    image: "/assets/OG.webp",
    title: "Ogilvy Experience",
    category: "Digital Project Manager | 2022 – Jan 2023",
    description: "Ogilvy Experience, a division of the global OGILVY Group and part of the WPP group."
  },
  {
    id: "project3",
    image: "/assets/ALT.webp",
    title: "Altaire",
    category: "Head of Digital Compliance & Projects | 2014 – Feb 2022",
    description: "Altaire specialise in custom AI solutions, email marketing and personalised data-driven campaigns using a proprietary software platform."
  },
  {
    id: "project4",
    image: "/assets/MCO.webp",
    title: "MARTIN&CO",
    category: "Head of Digital | 2012 – 2014",
    description: "Martin & Co - is the largest franchised property business in the UK with nearly 200 offices, managing over 30,000 properties nationwide."
  },
  {
    id: "project5",
    image: "/assets/PNCo.webp",
    title: "Panashco Media",
    category: "Head of Digital | 2009 – 2011",
    description: "Panashco Media is a full-service content marketing company, focused on its flagship product Property Life. A multi-channel consumer magazine, with the goal of helping Asian property investors with their purchases overseas."
  },
  {
    id: "project6",
    image: "/assets/PointP.webp",
    title: "KatalystM",
    category: "Product Owner | 2009 – 2011",
    description: "KatalystM are experts in ‘Lifecycle’ marketing, developing 1 to 1 communication strategies. KatalystM has a unique team of technical experts delivering complex CRM and customer data solutions."
  },
];

const WorkSection = () => {
  const titleRef = useScrollAnimation();
  const ctaRef = useScrollAnimation();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (project: Project) => {
    setSelectedProject(project);
    setIsModalOpen(true);
    // Prevent background scrolling when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setIsModalOpen(false);
    // Re-enable scrolling when modal is closed
    document.body.style.overflow = 'auto';
  };

  return (
    <section id="work" className="pt-32 pb-20 px-6 md:px-12 xl:px-24 bg-black text-white">
      <div className="max-w-[1920px] mx-auto">
        <h2
          ref={titleRef}
          className="font-serif text-4xl md:text-5xl font-medium tracking-heading mb-20 fade-in text-white"
        >
          Time Line
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
          {projects.map((project, index) => (
            <ProjectItem 
              key={project.id} 
              project={project} 
              index={index} 
              onClick={() => openModal(project)}
            />
          ))}
        </div>

        <div className="mt-16 text-center" ref={ctaRef}>
          <a
            href="#"
            className="inline-flex items-center border-b border-rich-black pb-1 group fade-in"
          >
            <span className="tracking-wider">View all projects</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
              />
            </svg>
          </a>
        </div>
      </div>

      {/* Project Modal */}
      <ProjectModal 
        project={selectedProject} 
        isOpen={isModalOpen} 
        onClose={closeModal} 
      />
    </section>
  );
};

interface ProjectItemProps {
  project: Project;
  index: number;
  onClick: () => void;
}

const ProjectItem = ({ project, index, onClick }: ProjectItemProps) => {
  const projectRef = useScrollAnimation();
  const isMobile = useIsMobile();

  return (
    <motion.div
      className="project-item relative overflow-hidden group cursor-pointer fade-in"
      ref={projectRef}
      initial={isMobile ? { opacity: 1, y: 0 } : { opacity: 0, y: 45 }}
      whileInView={isMobile ? { opacity: 1, y: 0 } : { opacity: 1, y: 0 }}
      transition={isMobile ? { duration: 0 } : { duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-100px" }}
      onClick={onClick}
    >
      <div className="aspect-[4/3] overflow-hidden">
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent pointer-events-none"></div>
      </div>
      <div className="project-overlay absolute inset-0 flex items-end p-6 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <div className="text-white">
          <h3 className="font-serif text-xl md:text-2xl tracking-heading mb-2">
            {project.title}
          </h3>
          <p className="text-sm tracking-wider text-light-gray">
            {project.category}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default WorkSection;
