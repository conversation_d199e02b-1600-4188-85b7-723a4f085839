import { useEffect, useRef } from "react";

interface UseScrollAnimationProps {
  threshold?: number;
  rootMargin?: string;
}

const useScrollAnimation = ({
  threshold = 0.1,
  rootMargin = "0px 0px -100px 0px",
}: UseScrollAnimationProps = {}) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible");
            observer.unobserve(entry.target);
          }
        });
      },
      { rootMargin, threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [rootMargin, threshold]);

  return ref;
};

export default useScrollAnimation;
