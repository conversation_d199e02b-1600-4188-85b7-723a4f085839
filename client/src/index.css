@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --primary: 207 90% 54%;
    --primary-foreground: 211 100% 99%;
    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --ring: 20 14.3% 4.1%;
    --radius: 0.5rem;

    /* Custom colors */
    --off-white: 0 0% 97%;
    --rich-black: 0 0% 7%;
    --medium-gray: 0 0% 47%;
    --light-gray: 0 0% 87%;
    --charcoal: 0 0% 20%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --primary: 207 90% 54%;
    --primary-foreground: 211 100% 99%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --ring: 240 4.9% 83.9%;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased bg-off-white text-rich-black;
    overflow-x: hidden;
  }

  * {
    @apply border-border;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .bg-off-white {
    background-color: hsl(var(--off-white));
  }
  
  .bg-rich-black {
    background-color: hsl(var(--rich-black));
  }
  
  .text-off-white {
    color: hsl(var(--off-white));
  }
  
  .text-rich-black {
    color: hsl(var(--rich-black));
  }
  
  .text-medium-gray {
    color: hsl(var(--medium-gray));
  }
  
  .text-light-gray {
    color: hsl(var(--light-gray));
  }
  
  .border-light-gray {
    border-color: hsl(var(--light-gray));
  }
  
  .tracking-heading {
    letter-spacing: 0.01em;
  }
  
  .tracking-wider {
    letter-spacing: 0.02em;
  }
  
  .hover-scale {
    @apply transition-transform duration-300 ease-in-out;
  }
  
  .hover-scale:hover {
    @apply transform scale-[1.02];
  }
  
  .fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }
  
  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slow-zoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}

.slow-zoom {
  animation: slow-zoom 20s infinite alternate ease-in-out;
}

